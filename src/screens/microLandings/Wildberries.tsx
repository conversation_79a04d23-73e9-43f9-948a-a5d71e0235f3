import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';

const features = [
    {
        title: 'Забудьте про дорогие фотосессии и дизайнеров!',
        description: 'С нашим AI-сервисом вы создаёте профессиональные изображения и инфографику для Wildberries и Ozon без студий и специалистов. Просто загрузите фотографию товара, и нейросеть легко впишет его в нужный вам фон',
        image: 'wildberries/1.jpg',
        reverse: false
    },
    {
        title: 'Легко управляйте ракурсами',
        description: 'Нужно создать картинки одного и того же товара с разных ракурсов? Холст.ИИ легко поможет вам создать недостающие изображения с любого ракурса',
        image: 'wildberries/2.jpg',
        reverse: true
    },
    {
        title: 'Легко изменяйте фото товара',
        description: 'Наш AI легко поможет добавить визуальные эффекты, например, капли влаги на фото увлажняющего крема. Забудьте о долгом редактировании изображений в фотошопе. Холст.ИИ за секунды изменит изображение вашего товара нужным образом',
        image: 'wildberries/3.jpg',
        reverse: false
    },
    {
        title: 'Легко удаляйте фон с фотографий товара',
        description: 'Забудьте о проблемах с удалением фона с фотографии или плохим качеством фотографии. С помощью Холст.ИИ вы легко можете преобразовать фото с телефона в готовую фотографию товара',
        image: 'wildberries/4.jpg',
        reverse: true
    },
    {
        title: 'Создавайте 3D-рендеры',
        description: 'Хотите сделать изображение как товар лежит в коробке? Наш AI позволяет легко создавать 3D-рендеры, чтобы вы могли изобразить товар наилучшим образом, без долгих переделок и ожидания дизайнеров.',
        image: 'wildberries/5.jpg',
        reverse: false
    }
]

const LandingWildberries = () => {
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title="Создавайте" titleHighlight="цепляющие карточки для товаров" subtitle="Загружайте товар — получайте крутые карточки! Нейросеть создаст изображения и инфографику для Wildberries по вашему описанию. Без фотосессий и дизайнеров — просто опишите кадр и получите яркий результат для площадки!" secondaryImage='/heroImages/wildberries/landingHero2.jpg' topSecondaryImage='/heroImages/wildberries/landingHero3.jpg' bottomSecondaryImage='/heroImages/wildberries/landingHero4.jpg'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingWildberries;
