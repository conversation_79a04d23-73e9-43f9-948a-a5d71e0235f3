import type { Metadata } from 'next';
import LandingWildberries from "@/screens/microLandings/Wildberries";

export const metadata: Metadata = {
  title: 'Создание карточки товара для Wildberries (ВБ) с ИИ - Холст.ИИ',
  description: 'Карточки товара для ВБ (Wildberries) с Холст.ИИ - замена фона, создание видео,  изображение по описанию',
  keywords: 'нейросеть для вб, обложки маркетплейс,обложки вайлдберрис,Wildberries, OZON, ЯндексМаркет,  Amazon, товарные фото, 3D рендеры, баннеры для акций',
  openGraph: {
    title: 'Создание карточки товара для Wildberries (ВБ) с ИИ - Холст.ИИ',
    description: 'Карточки товара для ВБ (Wildberries) с Холст.ИИ - замена фона, создание видео,  изображение по описанию',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Создание карточки товара для Wildberries (ВБ) с ИИ - Холст.ИИ',
    description: 'Карточки товара для ВБ (Wildberries) с Холст.ИИ - замена фона, создание видео,  изображение по описанию',
  },
};

export default function WildberriesPage() {
  return <LandingWildberries />;
}
